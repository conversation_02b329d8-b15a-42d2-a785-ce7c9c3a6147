# مشروع Dibla - منصة متاجر الذهب

مشروع متكامل لإدارة متاجر الذهب والمجوهرات يتكون من Backend (Node.js + Express + MongoDB) و Frontend (React + Vite).

## 📋 المتطلبات الأساسية

### متطلبات النظام
- Node.js (الإصدار 16 أو أحدث)
- MongoDB (الإصدار 4.4 أو أحدث)
- npm أو yarn
- Git

### تثبيت MongoDB

#### على Ubuntu/Debian:
```bash
# تحديث قائمة الحزم
sudo apt update

# تثبيت MongoDB
sudo apt install -y mongodb

# تشغيل MongoDB
sudo systemctl start mongodb
sudo systemctl enable mongodb

# التحقق من حالة MongoDB
sudo systemctl status mongodb
```

#### على macOS:
```bash
# تثبيت MongoDB باستخدام Homebrew
brew tap mongodb/brew
brew install mongodb-community

# تشغيل MongoDB
brew services start mongodb/brew/mongodb-community
```

#### على Windows:
1. قم بتحميل MongoDB من الموقع الرسمي
2. اتبع تعليمات التثبيت
3. تأكد من تشغيل خدمة MongoDB

## 🚀 تشغيل المشروع

### 1. إعداد Backend

```bash
# الانتقال إلى مجلد Backend
cd Gold-Backend

# تثبيت التبعيات
npm install

# إنشاء ملف .env (إذا لم يكن موجوداً)
cp .env.example .env

# تحرير ملف .env وإضافة المتغيرات المطلوبة
nano .env
```

#### متغيرات البيئة المطلوبة في Backend (.env):
```env
MONGO_URI=mongodb://127.0.0.1:27017/DEBLA
PORT=5000

GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
SESSION_SECRET=your-session-secret
CALLBACK_URL=http://localhost:5000/auth/google/callback

GMAIL_USER=<EMAIL>
GMAIL_APP_PASSWORD=your-app-password

JWT_ACCESS_SECRET=your-jwt-access-secret
JWT_REFRESH_SECRET=your-jwt-refresh-secret

NODE_ENV=development
```

```bash
# تشغيل Backend
npm start

# أو للتطوير مع إعادة التشغيل التلقائي
npm run dev
```

### 2. إعداد Frontend

```bash
# فتح terminal جديد والانتقال إلى مجلد Frontend
cd dibla-frontend

# تثبيت التبعيات
npm install

# التأكد من وجود ملف .env
# الملف موجود بالفعل مع الإعدادات الصحيحة

# تشغيل Frontend
npm run dev
```

## 🔧 الإعدادات

### Backend Configuration
- **المنفذ**: 5000
- **قاعدة البيانات**: MongoDB على المنفذ 27017
- **المصادقة**: JWT + Google OAuth

### Frontend Configuration
- **المنفذ**: 5173 (أو التالي المتاح)
- **API Base URL**: http://localhost:5000
- **Framework**: React + Vite

## 📱 الوصول للتطبيق

بعد تشغيل كلا من Backend و Frontend:

- **Frontend**: http://localhost:5173 (أو المنفذ المعروض)
- **Backend API**: http://localhost:5000
- **API Documentation**: http://localhost:5000/api-docs (إذا كان متوفراً)

## 🔐 المصادقة

### أنواع المستخدمين:
1. **Customer** (مستخدم عادي): تصفح المتاجر والمنتجات
2. **Seller** (صاحب متجر): إدارة المتجر والمنتجات
3. **Admin** (مدير): إدارة شاملة للنظام

### طرق تسجيل الدخول:
- تسجيل دخول عادي (email + password)
- تسجيل دخول بـ Google OAuth

## 📊 الميزات المتوفرة

### للمستخدمين العاديين:
- تصفح المتاجر والمنتجات
- إضافة المنتجات للمفضلة
- حجز المواعيد
- تقييم المتاجر

### لأصحاب المتاجر:
- إنشاء وإدارة المتجر
- إضافة وإدارة المنتجات
- إدارة الأوقات المتاحة للحجز
- عرض التقييمات

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة:

#### 1. خطأ في الاتصال بقاعدة البيانات:
```bash
# التحقق من تشغيل MongoDB
sudo systemctl status mongodb

# إعادة تشغيل MongoDB
sudo systemctl restart mongodb
```

#### 2. خطأ في المنفذ مستخدم:
```bash
# العثور على العملية التي تستخدم المنفذ
lsof -i :5000

# إيقاف العملية
kill -9 PID
```

#### 3. مشاكل في التبعيات:
```bash
# حذف node_modules وإعادة التثبيت
rm -rf node_modules package-lock.json
npm install
```

## 📝 ملاحظات مهمة

1. **قاعدة البيانات**: تأكد من تشغيل MongoDB قبل تشغيل Backend
2. **Google OAuth**: تحتاج لإعداد Google OAuth credentials للمصادقة
3. **CORS**: Backend مُعد للسماح بالطلبات من Frontend
4. **Environment Variables**: تأكد من إعداد جميع متغيرات البيئة المطلوبة

## 🔄 التحديثات المطبقة

تم ربط Frontend مع Backend وتطبيق المبدأ التالي:
- **Backend كمرجع أساسي**: تم تعديل Frontend ليتوافق مع APIs المتوفرة في Backend
- **إزالة الميزات غير المدعومة**: تم إزالة Facebook authentication
- **إضافة الميزات المفقودة**: تم إضافة Google OAuth integration
- **توحيد أنواع المستخدمين**: customer, seller, admin

## 🏗️ بنية المشروع

```
project1.1/
├── Gold-Backend/           # Backend (Node.js + Express)
│   ├── controllers/        # Controllers للـ APIs
│   ├── models/            # نماذج قاعدة البيانات
│   ├── routes/            # مسارات الـ APIs
│   ├── middlewares/       # Middleware functions
│   ├── utils/             # وظائف مساعدة
│   ├── sockets/           # WebSocket للدردشة
│   ├── .env               # متغيرات البيئة
│   ├── package.json       # تبعيات Backend
│   └── index.js           # نقطة البداية
│
├── dibla-frontend/        # Frontend (React + Vite)
│   ├── src/
│   │   ├── components/    # مكونات React
│   │   ├── pages/         # صفحات التطبيق
│   │   ├── services/      # خدمات API
│   │   ├── context/       # React Context
│   │   ├── hooks/         # Custom Hooks
│   │   ├── utils/         # وظائف مساعدة
│   │   └── styles/        # ملفات التنسيق
│   ├── .env               # متغيرات البيئة
│   ├── package.json       # تبعيات Frontend
│   └── vite.config.js     # إعدادات Vite
│
└── README.md              # هذا الملف
```

## 🔌 APIs المتوفرة

### Authentication
- `POST /auth/register` - تسجيل حساب جديد
- `POST /auth/login` - تسجيل الدخول
- `GET /auth/logout` - تسجيل الخروج
- `GET /auth/refresh` - تحديث التوكن
- `GET /auth/google` - تسجيل دخول بـ Google
- `GET /auth/google/callback` - Google OAuth callback

### Shops
- `GET /shop` - الحصول على جميع المتاجر
- `GET /shop/:id` - الحصول على متجر محدد
- `POST /shop/create` - إنشاء متجر جديد
- `PUT /shop/:id` - تحديث متجر
- `DELETE /shop/:id` - حذف متجر

### Products
- `GET /product` - الحصول على جميع المنتجات
- `GET /product/:id` - الحصول على منتج محدد
- `POST /product/create` - إنشاء منتج جديد
- `PUT /product/:id` - تحديث منتج
- `DELETE /product/:id` - حذف منتج
- `POST /product/favorite` - إضافة إلى المفضلة
- `GET /product/favorite/:id` - الحصول على المفضلة
- `DELETE /product/favorite/:id` - إزالة من المفضلة

### Booking
- `GET /booking/:shopId` - الحصول على الأوقات المتاحة
- `POST /booking/book` - حجز موعد
- `POST /booking` - إضافة وقت متاح

### Rating
- `POST /rate/:shopId` - تقييم متجر
- `GET /rate` - الحصول على التقييمات
- `GET /rate/:id` - الحصول على تقييم محدد
- `PUT /rate/:id` - تحديث تقييم
- `DELETE /rate/:id` - حذف تقييم

### User
- `GET /user/me` - الحصول على بيانات المستخدم
- `PUT /user` - تحديث المستخدم
- `PATCH /user/role` - تحديث دور المستخدم
- `DELETE /user` - حذف المستخدم
- `POST /user/forgot-password` - نسيان كلمة المرور
- `POST /user/reset-password/:token` - إعادة تعيين كلمة المرور

## 📞 الدعم

في حالة مواجهة أي مشاكل:
1. تحقق من logs في terminal
2. تأكد من تشغيل جميع الخدمات المطلوبة
3. راجع متغيرات البيئة
4. تحقق من الاتصال بقاعدة البيانات

## 🚀 نصائح للتطوير

1. **استخدم nodemon للتطوير**: `npm run dev` في Backend
2. **Hot Reload**: Frontend يدعم إعادة التحميل التلقائي
3. **API Testing**: استخدم Postman أو curl لاختبار APIs
4. **Database GUI**: استخدم MongoDB Compass لإدارة قاعدة البيانات

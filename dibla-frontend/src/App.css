@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  /* Golden Brown & Beige Theme - Light Mode */
  --background: #F8F4ED;  /* Light beige background */
  --foreground: #241C0F;  /* Dark brown text */
  --card: #FFFFFF;        /* White cards */
  --card-foreground: #241C0F;  /* Dark brown text on cards */
  --popover: #FFFFFF;     /* White popover */
  --popover-foreground: #241C0F;  /* Dark brown text on popover */
  --primary: #A37F41;     /* Golden brown primary */
  --primary-foreground: #FFFFFF;  /* White text on primary */
  --secondary: #E2D2B6;   /* Light beige secondary */
  --secondary-foreground: #241C0F;  /* Dark brown text on secondary */
  --muted: #F0E8DB;       /* Muted beige */
  --muted-foreground: #6D552C;  /* Medium brown for muted text */
  --accent: #D3BB92;      /* Accent beige */
  --accent-foreground: #241C0F;  /* Dark brown text on accent */
  --destructive: #B54A35; /* Reddish-brown for errors */
  --border: #E2D2B6;      /* Light border */
  --input: #E2D2B6;       /* Input border */
  --ring: #A37F41;        /* Focus ring */
  --chart-1: #A37F41;     /* Chart colors in golden brown theme */
  --chart-2: #C5A56D;
  --chart-3: #8A6C37;
  --chart-4: #D3BB92;
  --chart-5: #6D552C;
  --sidebar: #F8F4ED;     /* Sidebar background */
  --sidebar-foreground: #241C0F;  /* Sidebar text */
  --sidebar-primary: #A37F41;     /* Sidebar primary */
  --sidebar-primary-foreground: #FFFFFF;  /* Sidebar primary text */
  --sidebar-accent: #E2D2B6;      /* Sidebar accent */
  --sidebar-accent-foreground: #241C0F;   /* Sidebar accent text */
  --sidebar-border: #E2D2B6;      /* Sidebar border */
  --sidebar-ring: #A37F41;        /* Sidebar focus ring */
}

.dark {
  /* Golden Brown & Beige Theme - Dark Mode */
  --background: #120E07;     /* Very dark brown background */
  --foreground: #F0E8DB;     /* Light beige text */
  --card: #241C0F;           /* Dark surface for cards */
  --card-foreground: #F0E8DB; /* Light beige text on cards */
  --popover: #241C0F;        /* Dark popover */
  --popover-foreground: #F0E8DB; /* Light beige text on popover */
  --primary: #A37F41;        /* Same golden brown primary */
  --primary-foreground: #120E07; /* Dark text on primary */
  --secondary: #6D552C;      /* Darker secondary for dark mode */
  --secondary-foreground: #F0E8DB; /* Light text on secondary */
  --muted: #241C0F;          /* Muted dark */
  --muted-foreground: #C5A56D; /* Golden for muted text */
  --accent: #49391D;         /* Accent dark */
  --accent-foreground: #F0E8DB; /* Light text on accent */
  --destructive: #B54A35;    /* Same error color */
  --border: #49391D;         /* Dark border */
  --input: #49391D;          /* Input border */
  --ring: #A37F41;           /* Focus ring */
  --chart-1: #A37F41;        /* Chart colors for dark mode */
  --chart-2: #C5A56D;
  --chart-3: #8A6C37;
  --chart-4: #D3BB92;
  --chart-5: #6D552C;
  --sidebar: #120E07;        /* Sidebar background */
  --sidebar-foreground: #F0E8DB; /* Sidebar text */
  --sidebar-primary: #A37F41;    /* Sidebar primary */
  --sidebar-primary-foreground: #120E07; /* Sidebar primary text */
  --sidebar-accent: #49391D;     /* Sidebar accent */
  --sidebar-accent-foreground: #F0E8DB;  /* Sidebar accent text */
  --sidebar-border: #49391D;     /* Sidebar border */
  --sidebar-ring: #A37F41;       /* Sidebar focus ring */
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
